<!-- This template is to serve as a comprehensive outline for documenting updates to the code. Some of these sections may not be relevant to your PR. In that case, document it inline with `N/A`  -->

## What does this PR do?

<!-- Provide a clear and concise description of what this PR accomplishes -->

### Related Issues & PRs

<!-- List the related issues, using either: `Closes #123` or `Fixes #123` , which will automatically move the specified issue to "In Review" when the PR is created -->

#### Key Decisions

<!-- Explain the technical decisions and alternative approaches considered for your new implementations and refactored solutions -->

- **Implemeneted Approach**:
  <!-- What did you decide on? -->
- **Alternatives Considered**:
  <!-- What else did you consider? -->
- **Rationale**:
  <!-- Why did you decide this was the most appropriate solution? -->

## Breaking Changes

<!-- Do others need to do something to successfully incorporate your changes into their work? -->

- [ ] This PR includes breaking changes
<!-- What do they need to do? -->

### Limitations & Concerns

<!-- Describe any limitations, potential edge cases, or situations where your solution might break -->

- **Known limitations**:
- **Edge cases**:
- **Potential risks**:

---

### UI Changes

<!-- If this PR modifies the UI, provide screenshots and descriptions -->

- [ ] This PR includes UI changes
<!-- Include screenshots in the table below, include before & after applicable -->

### Screenshots

| Description | Screenshot      |
| ----------- | --------------- |
| Before      | <!-- img(s) --> |
| After       | <!-- img(s) --> |

---

### Reviewers

<!-- Tag at least 2 others for review -->

- [ ] Reviewer 1: @username
- [ ] Reviewer 2: @username

### Next Steps

<!-- What needs to happen after this PR is merged? -->

- [ ] Follow-up tasks are created
  - [ ] Task 1

### How to Test

<!-- Provide detailed steps for QA to test the changes -->

1. Prerequisites:
   <!-- e.g. Do I need to be a specific role? -->
2. Test steps:
   <!-- What is the quickest path to testing your solution? -->
3. Expected results:
   <!-- Is there any nuance that should be clarified? If so, can the implementation get more simple instead? -->

## Final Checklist

- [ ] Linked related issues
- [ ] Rebased with target branch
- [ ] No lint errors
- [ ] QA'd in Chrome, Safari & Firefox
- [ ] QA'd mobile experience
- [ ] Added/updated tests (if applicable)
- [ ] Mobile responsive (if applicable)
- [ ] Updated documentation (if applicable)
- [ ] Notified the Team of this updates readiness

---

## AI Usage

<!-- Document any AI tools you used to develop this -->

- [ ] AI tools were used in development
  - **Tool used**:
    <!-- Specify any model used -->
  - **Prompts used**:
    <!-- Format these inclusions as quotes, like: `> Provide  -->
    - **Initial prompt**:
- [ ] No AI tools were used
  - **Reason**:
    <!-- Why not? -->

## References

<!-- List any helpful references used during development -->

- **Documentation**:
- **Discussions**:
- **Related PRs**:
