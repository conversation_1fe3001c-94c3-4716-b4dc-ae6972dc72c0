Augment Code Session Starter

Please load and follow the instructions from these files:

    llm/svelte5_full_context.txt
    llm/svelte5_typescript.txt

Read both files completely and use their contents as your instructions for this session.

Also, please following these coding style guidelines:

    Use code comments sparingly. Don't add comments that are obvious.
    Use arrow functions where possible
    Use semicolons
    Functions that handle the onSubmit name onSubmit
    Where possible, create atomic components with the Svelte 5 handling of props
    Make sure components are only given the props they need

When you make changes, run the following checks:

    Make sure your changes are best-practice for Svelte 5 and don't use deprecated features, such as on:click, or the Svelte 4 way of handling stores and events.
    Runsv check to make sure there are no errors.
