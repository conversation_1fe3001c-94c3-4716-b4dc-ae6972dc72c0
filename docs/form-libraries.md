# Form Libraries in Our Project

## [formsnap](https://www.formsnap.dev/docs)

Formsnap is a [UI component](https://next.shadcn-svelte.com/docs/components/form) library for Svelte that
provides form primitives. We use it for:

- Creating accessible form components with proper ARIA attributes
- Providing a consistent component API across our form elements
- Handling form field state management (errors, validation, etc.)
- Building composable form components that work together seamlessly

Formsnap serves as the foundation for our form UI components, providing the underlying functionality while
our custom components add styling and project-specific behavior.

References:

- [Homepage](https://www.formsnap.dev/docs)
- [Github](https://github.com/svecosystem/formsnap)

## [sveltekit-superforms](https://superforms.rocks/)

Sveltekit-superforms is a form management library specifically designed for SvelteKit applications. We use
it for:

- Form validation with schema support (works with <PERSON><PERSON>, <PERSON><PERSON>, etc.)
- Type-safe form handling with TypeScript integration
- Server-side validation with client-side feedback
- Progressive enhancement (forms work without JavaScript)
- Simplified form state management
- Handling form submissions with proper error handling
- Managing form data throughout the form lifecycle

References:

- [Homepage](https://superforms.rocks/)
- [Github](https://github.com/ciscoheat/sveltekit-superforms)
- [Intro video](https://www.youtube.com/watch?v=MiKzH3kcVfs)

## Combination

Together, these libraries provide a complete form solution:

- **formsnap**: Handles the UI components and accessibility
- **sveltekit-superforms**: Manages form data, validation, and submission

This combination allows us to build robust, accessible, and type-safe forms with minimal boilerplate code.
