<script lang="ts">
  import { Switch as SwitchPrimitive, type WithoutChildrenOrChild } from 'bits-ui';
  import { cn } from '$lib/utils.js';
  import Check from 'lucide-svelte/icons/check';

  let {
    ref = $bindable(null),
    class: className,
    checked = $bindable(false),
    ...restProps
  }: WithoutChildrenOrChild<SwitchPrimitive.RootProps> = $props();
</script>

<SwitchPrimitive.Root
  bind:ref
  bind:checked
  class={cn(
    'peer inline-flex h-8 w-12 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input',
    className,
  )}
  {...restProps}
>
  <SwitchPrimitive.Thumb
    class={cn(
      'pointer-events-none relative flex size-7 items-center justify-center rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0',
    )}
  >
    {#if checked}
      <Check class="absolute size-5 text-primary opacity-100 transition-opacity" />
    {/if}
  </SwitchPrimitive.Thumb>
</SwitchPrimitive.Root>
