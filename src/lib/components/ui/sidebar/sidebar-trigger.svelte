<script lang="ts">
  import { Button } from '$lib/components/ui/button/index.js';
  import { cn } from '$lib/utils.js';
  import type { ComponentProps } from 'svelte';
  import { useSidebar } from './context.svelte.js';
  import { ChevronLeft, Menu } from 'lucide-svelte';

  let {
    ref = $bindable(null),
    class: className,
    onclick,
    ...restProps
  }: ComponentProps<typeof Button> & {
    onclick?: (e: MouseEvent) => void;
  } = $props();

  const sidebar = useSidebar();
</script>

<Button
  type="button"
  onclick={(e) => {
    onclick?.(e);
    sidebar?.toggle();
  }}
  data-sidebar="trigger"
  variant="ghost"
  size="icon"
  class={cn('h-10 w-10', className)}
  {...restProps}
>
  {#if sidebar?.open && !sidebar?.isMobile}
    <ChevronLeft />
  {:else}
    <Menu />
  {/if}
  <span class="sr-only">Toggle Sidebar</span>
</Button>
