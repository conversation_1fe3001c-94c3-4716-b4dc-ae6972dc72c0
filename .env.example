########################################################################################################################
### Environment
########################################################################################################################

# APP_ENVIRONMENT = [ production | staging | testing | development ]
PUBLIC_APP_ENVIRONMENT=development
# LOG_LEVEL = [ 'debug' | 'info' | 'warn' | 'error' | 'silent' ]
PUBLIC_LOG_LEVEL=debug

########################################################################################################################
### Site
########################################################################################################################

PUBLIC_SITE_URL="http://localhost:5173"
PUBLIC_FSDATA_URL="http://localhost:8092/fsdata/api/graphql"
PUBLIC_MOCK_DATA=false

########################################################################################################################
### Cloudflare
########################################################################################################################

# Development Sitekey (always passes):
PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY="1x00000000000000000000AA"